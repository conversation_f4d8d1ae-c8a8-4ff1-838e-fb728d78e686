import sys
import json
import os
import subprocess
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QPushButton, QLabel, QTextEdit, QComboBox,
                               QProgressBar, QScrollArea, QFrame, QMessageBox, QListWidget,
                               QSplitter, QGroupBox, QGridLayout, QLineEdit, QListWidgetItem)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal as Signal, QRect, QPoint
from PyQt5.QtGui import QPixmap, QFont, QPalette, QColor, QPainter, QPen, QBrush
import pyttsx3
import threading

class VoiceThread(QThread):
    """语音播放线程"""
    def __init__(self, text):
        super().__init__()
        self.text = text

    def run(self):
        try:
            engine = pyttsx3.init()
            engine.setProperty('rate', 220)  # 语速
            engine.setProperty('volume', 1)  # 音量
            engine.say(self.text)
            engine.runAndWait()
        except Exception:
            pass  # 语音播放失败时静默处理

class FlowchartWidget(QWidget):
    """横向流程图显示组件"""
    voice_clicked = Signal(int)  # 添加语音点击信号

    def __init__(self):
        super().__init__()
        self.current_step = 0
        self.steps = []
        self.setMinimumHeight(200)
        self.setMaximumHeight(200)
        self.speaker_rects = []  # 存储喇叭图标的位置
        self.show_welcome = True  # 添加欢迎界面标志

    def update_flowchart(self, steps):
        """更新流程图"""
        self.steps = steps
        self.show_welcome = False  # 有流程时隐藏欢迎界面
        self.update()

    def highlight_current_step(self, step_index):
        """高亮当前步骤"""
        self.current_step = step_index
        self.update()

    def paintEvent(self, event):
        """绘制流程图"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        if not self.steps and self.show_welcome:
            # 显示使用提示
            painter.setFont(QFont("Microsoft YaHei", 15))
            painter.setPen(QPen(QColor(120, 120, 120)))
            tip_text = "请在下方输入你想要的调试与测试操作流程名称即可生成操作流程"
            painter.drawText(self.rect(), Qt.AlignCenter, tip_text)
            return
        
        if not self.steps:
            return

        # 清空喇叭图标位置记录
        self.speaker_rects = []

        # 计算每个步骤框的位置和大小
        widget_width = self.width()
        widget_height = self.height()

        step_count = len(self.steps)
        if step_count == 0:
            return

        # 计算步骤框的尺寸和间距
        margin = 20
        arrow_width = 30
        available_width = widget_width - 2 * margin - (step_count - 1) * arrow_width
        step_width = available_width // step_count
        step_height = 80
        step_y = (widget_height - step_height) // 2

        # 绘制每个步骤
        for i, step in enumerate(self.steps):
            step_x = margin + i * (step_width + arrow_width)

            # 设置颜色 - 简洁的配色方案
            if i == self.current_step:
                # 当前步骤 - 蓝色高亮
                brush = QBrush(QColor(33, 150, 243))   # 蓝色
                pen = QPen(QColor(25, 118, 210), 2)    # 深蓝色边框
                text_color = QColor(255, 255, 255)     # 白色文字
            elif i < self.current_step:
                # 已完成步骤 - 浅灰色
                brush = QBrush(QColor(245, 245, 245))  # 浅灰色
                pen = QPen(QColor(224, 224, 224), 1)   # 灰色边框
                text_color = QColor(158, 158, 158)     # 灰色文字
            else:
                # 未开始步骤 - 白色
                brush = QBrush(QColor(255, 255, 255))  # 白色
                pen = QPen(QColor(224, 224, 224), 1)   # 浅灰色边框
                text_color = QColor(97, 97, 97)        # 深灰色文字

            # 绘制矩形框
            painter.setBrush(brush)
            painter.setPen(pen)
            rect = QRect(step_x, step_y, step_width, step_height)
            painter.drawRoundedRect(rect, 8, 8)

            # 绘制步骤文字
            painter.setPen(QPen(text_color))
            painter.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))

            # 步骤编号
            number_text = f"{i + 1}"
            painter.drawText(step_x + 8, step_y + 18, number_text)

            # 步骤标题（截断过长的文字）
            title = step['title']
            if len(title) > 6:
                title = title[:6] + "..."

            painter.setFont(QFont("Microsoft YaHei", 13))
            painter.drawText(step_x + 8, step_y + 35, step_width - 16, 40,
                           Qt.AlignLeft | Qt.TextWordWrap, title)

            # 绘制箭头（除了最后一个步骤）
            if i < step_count - 1:
                arrow_x = step_x + step_width + 8
                arrow_y = step_y + step_height // 2

                painter.setPen(QPen(QColor(189, 189, 189), 1))
                # 箭头线
                painter.drawLine(arrow_x, arrow_y, arrow_x + arrow_width - 16, arrow_y)
                # 箭头头部
                painter.drawLine(arrow_x + arrow_width - 16, arrow_y,
                               arrow_x + arrow_width - 20, arrow_y - 4)
                painter.drawLine(arrow_x + arrow_width - 16, arrow_y,
                               arrow_x + arrow_width - 20, arrow_y + 4)

            # 绘制喇叭图标
            speaker_size = 18
            speaker_x = step_x + step_width - speaker_size - 8
            speaker_y = step_y + 8
            speaker_rect = QRect(speaker_x, speaker_y, speaker_size, speaker_size)

            # 保存喇叭图标位置用于点击检测
            self.speaker_rects.append((speaker_rect, i))

            # 绘制喇叭图标背景 - 简洁的圆形背景
            painter.setBrush(QBrush(QColor(97, 97, 97, 180)))  # 深灰色半透明背景
            painter.setPen(QPen(QColor(97, 97, 97), 1))
            painter.drawEllipse(speaker_rect)

            # 绘制喇叭符号
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.setFont(QFont("Arial", 11))
            painter.drawText(speaker_rect, Qt.AlignCenter, "♪")

    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            click_pos = event.pos()
            # 检查是否点击了喇叭图标
            for speaker_rect, step_index in self.speaker_rects:
                if speaker_rect.contains(click_pos):
                    self.voice_clicked.emit(step_index)
                    break

class ProcessFlowAssistant(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_process = None
        self.current_step_index = 0
        self.process_data = {}

        self.init_ui()
        self.load_process_data()
        self.show_welcome_content()  # 添加欢迎内容显示

    def init_ui(self):
        self.setWindowTitle("流程生成助手")
        self.setGeometry(0, 0, 1000, 1000)
        self.setFixedSize(1900, 1000)

        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 使用共享的UI创建方法
        self._create_main_ui(central_widget)

    def _create_main_ui(self, parent_widget):
        """创建主要UI布局 - 共享方法"""
        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(parent_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 顶部标题区域
        title_label = self._create_title_label()
        main_layout.addWidget(title_label)

        # 完成UI布局创建
        self._complete_ui_layout(main_layout, title_label)

    def _create_title_label(self):
        """创建标题标签 - 共享方法"""
        title_label = QLabel("流程生成助手")
        title_label.setAlignment(Qt.AlignCenter)
        # 明确设置字体大小，确保生效
        font = QFont("Microsoft YaHei", 40, QFont.Bold)
        font.setPointSize(40)  # 明确设置点大小
        title_label.setFont(font)
        title_label.setStyleSheet("""
            QLabel {
                color: #333333;
                padding: 15px 0px;
                background-color: transparent;
                font-size: 40px;  /* 在样式表中也设置字体大小 */
                font-weight: bold;
            }
        """)
        return title_label

    def _complete_ui_layout(self, main_layout, title_label):
        """完成UI布局创建 - 共享方法"""
        # 流程图区域
        self.flowchart = FlowchartWidget()
        # 连接喇叭点击信号
        self.flowchart.voice_clicked.connect(self.play_voice_for_step)
        main_layout.addWidget(self.flowchart)

        # 中间内容区域 - 水平布局
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)

        # 技能要点区域
        skills_panel = self.create_skills_panel()
        content_layout.addWidget(skills_panel)

        # 所需工具区域
        tools_panel = self.create_tools_panel()
        content_layout.addWidget(tools_panel)

        # 注意事项区域
        cautions_panel = self.create_cautions_panel()
        content_layout.addWidget(cautions_panel)

        main_layout.addLayout(content_layout)

        # 添加弹性空间，将底部组件推到下方
        main_layout.addStretch()

        # 底部流程选择区域
        process_panel = self.create_process_selection_panel()
        main_layout.addWidget(process_panel)

        # 控制按钮区域 - 移动到输入框下方
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # 设置布局比例
        main_layout.setStretchFactor(title_label, 0)  # 标题固定高度
        main_layout.setStretchFactor(self.flowchart, 3)  # 流程图增加空间
        main_layout.setStretchFactor(content_layout, 2)  # 三栏内容减少空间
        main_layout.setStretchFactor(process_panel, 0)  # 输入框固定高度
        main_layout.setStretchFactor(control_panel, 0)  # 控制按钮固定高度

    def create_skills_panel(self):
        """创建技能要点面板"""
        group = QGroupBox("技能要点")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: 500;
                font-size: 17px;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        self.skills_text = QTextEdit()
        self.skills_text.setReadOnly(True)
        self.skills_text.setMaximumHeight(600)
        self.skills_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px;
                background-color: #fafafa;
                font-size: 22px;
                font-family: "Microsoft YaHei";
                color: #333333;
                line-height: 1.6;
            }
        """)

        layout.addWidget(self.skills_text)
        return group

    def create_tools_panel(self):
        """创建所需工具面板"""
        group = QGroupBox("所需工具")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: 500;
                font-size: 17px;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        # 工具列表
        self.tools_list = QListWidget()
        self.tools_list.setMaximumHeight(600)
        self.tools_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #fafafa;
                font-size: 22px;
                font-family: "Microsoft YaHei";
                color: #333333;
            }
            QListWidget::item {
                padding: 6px 6px;
                border-bottom: 1px solid #f0f0f0;
                margin-bottom: 4px;
            }
            QListWidget::item:selected {
                background-color: #f5f5f5;
                color: #2196F3;
            }
        """)

        layout.addWidget(self.tools_list)
        return group

    def create_cautions_panel(self):
        """创建注意事项面板"""
        group = QGroupBox("注意事项")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: 500;
                font-size: 17px;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        self.cautions_text = QTextEdit()
        self.cautions_text.setReadOnly(True)
        self.cautions_text.setMaximumHeight(600)
        self.cautions_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px;
                background-color: #fff8e1;
                color: #e65100;
                font-size: 22px;
                font-family: "Microsoft YaHei";
                line-height: 1.6;
            }
        """)

        layout.addWidget(self.cautions_text)
        return group

    def create_control_panel(self):
        """创建控制面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 5, 0, 10)  # 进一步减少上边距
        layout.setSpacing(5)  # 减少间距

        # 控制按钮区域
        button_layout = QHBoxLayout()

        self.prev_button = QPushButton("上一步")
        self.prev_button.clicked.connect(self.prev_step)
        self.prev_button.setEnabled(False)
        self.prev_button.setStyleSheet("""
            QPushButton {
                background-color: #ffffff;
                color: #666666;
                font-weight: 100;
                padding: 12px 24px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 15px;
            }
            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #2196F3;
                color: #2196F3;
            }
            QPushButton:disabled {
                background-color: #fafafa;
                color: #cccccc;
                border-color: #f0f0f0;
            }
        """)

        self.confirm_button = QPushButton("下一步")
        self.confirm_button.clicked.connect(self.confirm_step)
        self.confirm_button.setEnabled(False)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: 100;
                padding: 12px 32px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #999999;
            }
        """)

        self.link_button = QPushButton("查看文档")
        self.link_button.clicked.connect(self.open_document)
        self.link_button.setEnabled(False)
        self.link_button.setStyleSheet("""
            QPushButton {
                background-color: #ffffff;
                color: #666666;
                font-weight: 100;
                padding: 10px 20px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #2196F3;
                color: #2196F3;
            }
            QPushButton:disabled {
                background-color: #fafafa;
                color: #cccccc;
                border-color: #f0f0f0;
            }
        """)

        button_layout.addWidget(self.prev_button)
        button_layout.addStretch()
        
        # 进度显示 - 移动到按钮中间
        progress_layout = QHBoxLayout()
        progress_label = QLabel("当前进度:")
        progress_label.setFont(QFont("Arial", 11))

        self.progress_label = QLabel("0/0")
        self.progress_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.progress_label.setStyleSheet("color: #2196F3;")

        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_label)
        
        button_layout.addLayout(progress_layout)
        button_layout.addStretch()
        
        button_layout.addWidget(self.confirm_button)
        button_layout.addWidget(self.link_button)

        # 移除原来的进度显示部分
        # progress_layout = QHBoxLayout()
        # ...原来的进度显示代码删除...

        layout.addLayout(button_layout)
        # layout.addLayout(progress_layout)  # 删除这行

        return widget

    def create_process_selection_panel(self):
        """创建流程输入面板"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 10, 0, 5)  # 减少下边距

        process_label = QLabel("请输入需要生成的流程:")
        process_label.setFont(QFont("Microsoft YaHei", 15, QFont.Medium))
        process_label.setStyleSheet("color: #333333;")

        self.process_input = QLineEdit()
        self.process_input.setMinimumWidth(1320)
        self.process_input.setPlaceholderText("例如：毫米波雷达调试、激光雷达标定、摄像头标定、设备维护等")
        self.process_input.setStyleSheet("""
            QLineEdit {
                padding: 12px 16px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 15px;
                background-color: #ffffff;
                color: #333333;
            }
            QLineEdit:focus {
                border-color: #2196F3;
                outline: none;
            }
            QLineEdit::placeholder {
                color: #999999;
            }
        """)
        # 添加回车键触发生成
        self.process_input.returnPressed.connect(self.generate_process)

        self.generate_button = QPushButton("点击生成")
        self.generate_button.clicked.connect(self.generate_process)
        self.generate_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: 500;
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #999999;
            }
        """)

        # 添加状态提示标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 13px;
                padding: 4px 8px;
            }
        """)

        layout.addWidget(process_label)
        layout.addWidget(self.process_input)
        layout.addWidget(self.generate_button)
        layout.addWidget(self.status_label)
        layout.addStretch()

        return widget



    def load_process_data(self):
        """加载流程数据"""
        try:
            with open('process_flow.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.process_data = data.get('processes', {})

        except FileNotFoundError:
            QMessageBox.warning(self, "警告", "未找到 process_flow.json 文件")
            self.process_data = {}
        except json.JSONDecodeError:
            QMessageBox.warning(self, "警告", "JSON 文件格式错误")
            self.process_data = {}
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载流程数据失败: {str(e)}")
            self.process_data = {}

    def generate_process(self):
        """智能生成流程"""
        user_input = self.process_input.text().strip()
        if not user_input:
            #QMessageBox.warning(self, "提示", "请输入流程描述")
            return

        #self.status_label.setText("🔍 正在分析关键词...")

        # 关键词匹配和流程生成
        matched_process = self.match_process_by_keywords(user_input)

        if matched_process:
            self.current_process = matched_process
            self.current_step_index = 0

            # 更新流程图
            self.flowchart.update_flowchart(self.current_process)

            # 显示第一步
            self.update_ui()

            #self.status_label.setText("✅ 流程生成成功")
        #else:
            #self.status_label.setText("❌ 未找到匹配的流程")

    def match_process_by_keywords(self, user_input):
        """根据关键词匹配流程"""
        user_input_lower = user_input.lower()

        # 定义关键词映射
        keyword_mapping = {
            "毫米波雷达调试与测试操作流程": [
                "毫米波", "雷达", "调试", "测试", "radar", "毫米波雷达"
            ],
            "激光雷达标定流程": [
                "激光雷达", "激光", "lidar", "标定", "激光标定"
            ],
            "摄像头标定流程": [
                "摄像头", "相机", "camera", "标定", "摄像头标定", "相机标定"
            ],
            "设备维护流程": [
                "设备", "维护", "保养", "维修", "检查", "清洁"
            ]
        }

        # 计算匹配分数
        best_match = None
        best_score = 0

        for process_name, keywords in keyword_mapping.items():
            if process_name in self.process_data:
                score = 0
                for keyword in keywords:
                    if keyword in user_input_lower:
                        score += 1

                if score > best_score:
                    best_score = score
                    best_match = process_name

        # 如果有匹配，返回对应的流程数据
        if best_match and best_score > 0:
            return self.process_data[best_match]

        return None

    def update_ui(self):
        """更新界面显示"""
        if not self.current_process or self.current_step_index >= len(self.current_process):
            # 清空所有显示
            self.skills_text.setText("请选择流程开始操作")
            self.tools_list.clear()
            self.cautions_text.setText("请选择流程开始操作")
            self.confirm_button.setEnabled(False)
            self.link_button.setEnabled(False)
            return

        current_step = self.current_process[self.current_step_index]

        # 更新技能要点（使用描述信息）
        skills_content = f"步骤 {self.current_step_index + 1}: {current_step['title']}\n\n"
        skills_content += f"操作说明:\n{current_step['description']}"
        self.skills_text.setText(skills_content)

        # 更新工具列表
        self.tools_list.clear()
        for i, tool in enumerate(current_step.get('tools', []), 1):
            # 处理新的工具对象格式
            if isinstance(tool, dict):
                tool_name = tool.get('name', '未知工具')
                tool_image = tool.get('image', '')
                self.add_tool_item(i, tool_name, tool_image)
            else:
                # 兼容旧的字符串格式
                self.tools_list.addItem(f"{i}. {tool}")

        # 更新注意事项
        cautions_content = current_step.get('cautions', '')
        if cautions_content:
            # 将注意事项格式化为编号列表
            cautions_lines = cautions_content.split('。')
            formatted_cautions = ""
            for i, line in enumerate(cautions_lines, 1):
                if line.strip():
                    formatted_cautions += f"{i}. {line.strip()}。\n"
            self.cautions_text.setText(formatted_cautions)
        else:
            self.cautions_text.setText("暂无特殊注意事项")

        # 更新按钮状态
        self.prev_button.setEnabled(self.current_step_index > 0)
        self.confirm_button.setEnabled(True)
        self.link_button.setEnabled(bool(current_step.get('link_url')))

        # 高亮当前步骤
        self.flowchart.highlight_current_step(self.current_step_index)

        # 更新进度
        self.update_progress()

    def add_tool_item(self, index, tool_name, tool_image_path):
        """添加带图片的工具项到列表中"""
        import os

        # 创建自定义widget
        item_widget = QWidget()
        layout = QHBoxLayout(item_widget)
        layout.setContentsMargins(0, 0, 150, 15)
        layout.setSpacing(12)

        # 添加工具图片
        image_label = QLabel()
        image_label.setFixedSize(80, 80)
        image_label.setStyleSheet("""
            QLabel {
                border: 1px solid #e0e0e0;
                border-radius: 14px;
                background-color: #f9f9f9;
            }
        """)

        # 加载图片
        if tool_image_path and os.path.exists(tool_image_path):
            pixmap = QPixmap(tool_image_path)
            if not pixmap.isNull():
                # 缩放图片以适应标签大小
                scaled_pixmap = pixmap.scaled(400, 400, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label.setPixmap(scaled_pixmap)
                image_label.setAlignment(Qt.AlignCenter)
            else:
                # 图片加载失败，显示默认文本
                image_label.setText("图片")
                image_label.setAlignment(Qt.AlignCenter)
                image_label.setStyleSheet(image_label.styleSheet() + "color: #999999; font-size: 60px;")
        else:
            # 没有图片路径或文件不存在，显示默认图标
            image_label.setText("🔧")
            image_label.setAlignment(Qt.AlignCenter)
            image_label.setStyleSheet(image_label.styleSheet() + "font-size: 60px;")

        # 添加工具名称
        name_label = QLabel(f"{index}. {tool_name}")
        name_label.setStyleSheet("""
            QLabel {
                color: #333333;
                font-size: 22px;
                font-family: "Microsoft YaHei";
                border: none;
                background: transparent;
            }
        """)

        # 调整布局：先显示文字，再显示图片
        layout.addWidget(name_label)
        layout.addStretch()
        layout.addWidget(image_label)

        # 创建列表项
        list_item = QListWidgetItem()
        list_item.setSizeHint(item_widget.sizeHint())

        # 添加到列表
        self.tools_list.addItem(list_item)
        self.tools_list.setItemWidget(list_item, item_widget)

    def update_progress(self):
        """更新进度显示"""
        if not self.current_process:
            self.progress_label.setText("0/0")
            return

        total_steps = len(self.current_process)
        completed_steps = self.current_step_index + 1  # 加1因为索引从0开始

        self.progress_label.setText(f"{completed_steps}/{total_steps}")

    def play_voice(self):
        """播放当前步骤语音"""
        if not self.current_process or self.current_step_index >= len(self.current_process):
            return

        current_step = self.current_process[self.current_step_index]
        text_to_speak = f"{current_step['title']}。{current_step['description']}"

        # 在新线程中播放语音
        self.voice_thread = VoiceThread(text_to_speak)
        self.voice_thread.start()

    def play_voice_for_step(self, step_index):
        """播放指定步骤的语音"""
        if not self.current_process or step_index >= len(self.current_process):
            return

        step = self.current_process[step_index]
        text_to_speak = f"{step['title']}。{step['description']}"

        # 在新线程中播放语音
        self.voice_thread = VoiceThread(text_to_speak)
        self.voice_thread.start()

    def open_document(self):
        """打开相关文档"""
        if not self.current_process or self.current_step_index >= len(self.current_process):
            return

        current_step = self.current_process[self.current_step_index]
        link_url = current_step.get('link_url')

        if link_url and os.path.exists(link_url):
            try:
                # 在Windows上使用默认程序打开文件
                os.startfile(link_url)
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法打开文档: {str(e)}")
        else:
            QMessageBox.information(self, "提示", "文档文件不存在")

    def prev_step(self):
        """上一步"""
        if self.current_step_index > 0:
            self.current_step_index -= 1
            self.update_ui()

    def confirm_step(self):
        """确认当前步骤完成"""
        if not self.current_process:
            return

        # 移动到下一步
        if self.current_step_index < len(self.current_process) - 1:
            self.current_step_index += 1
            self.update_ui()
        else:
            # 流程完成
            QMessageBox.information(self, "完成", "🎉 恭喜！所有流程步骤已完成！")
            self.show_welcome_content()

    def show_welcome_content(self):
        """显示欢迎内容"""
        # 技能要点显示系统介绍
        welcome_skills = """系统功能介绍

• 智能流程生成：输入流程名称自动匹配相关操作步骤
• 语音播放：点击喇叭图标播放操作说明
• 文档链接：快速访问相关技术文档

开始使用：请在下方输入您需要的流程名称
    """
        self.skills_text.setText(welcome_skills)
        
        # 所需工具显示支持的流程类型
        welcome_tools = [
            "功能介绍：",
            "这里显示对应流程需要使用的工具名称和图片"
        ]
        self.tools_list.clear()
        for tool in welcome_tools:
            self.tools_list.addItem(tool)
        
        # 注意事项显示使用提示
        welcome_cautions = """使用提示

1. 在底部输入框中输入流程关键词
2. 点击"智能生成"按钮或按回车键
3. 系统将自动匹配并生成相应流程
4. 按步骤执行，点击"确认完成"进入下一步
    """
        self.cautions_text.setText(welcome_cautions)

# 创建一个兼容的 ChatRobotWidget 类，用于嵌入到主窗口中
class ChatRobotWidget(QWidget):
    """流程生成助手组件 - 兼容主窗口的版本"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_process = None
        self.current_step_index = 0
        self.process_data = {}

        self.init_ui()
        self.load_process_data()
        self.show_welcome_content()

    def init_ui(self):
        """初始化用户界面"""
        # 使用共享的UI创建方法
        self._create_main_ui(self)

    def _create_title_label(self):
        """创建标题标签 - 共享方法"""
        return ProcessFlowAssistant._create_title_label(self)

    def _complete_ui_layout(self, main_layout, title_label):
        """完成UI布局创建 - 共享方法"""
        return ProcessFlowAssistant._complete_ui_layout(self, main_layout, title_label)

    def _create_main_ui(self, parent_widget):
        """创建主要UI布局 - 共享方法"""
        return ProcessFlowAssistant._create_main_ui(self, parent_widget)

    def create_skills_panel(self):
        """创建技能要点面板"""
        return ProcessFlowAssistant.create_skills_panel(self)

    def create_tools_panel(self):
        """创建所需工具面板"""
        return ProcessFlowAssistant.create_tools_panel(self)

    def create_cautions_panel(self):
        """创建注意事项面板"""
        return ProcessFlowAssistant.create_cautions_panel(self)

    def create_control_panel(self):
        """创建控制面板"""
        return ProcessFlowAssistant.create_control_panel(self)

    def create_process_selection_panel(self):
        """创建流程输入面板"""
        return ProcessFlowAssistant.create_process_selection_panel(self)

    def load_process_data(self):
        """加载流程数据"""
        return ProcessFlowAssistant.load_process_data(self)

    def generate_process(self):
        """智能生成流程"""
        return ProcessFlowAssistant.generate_process(self)

    def match_process_by_keywords(self, user_input):
        """根据关键词匹配流程"""
        return ProcessFlowAssistant.match_process_by_keywords(self, user_input)

    def update_ui(self):
        """更新界面显示"""
        return ProcessFlowAssistant.update_ui(self)

    def add_tool_item(self, index, tool_name, tool_image_path):
        """添加带图片的工具项到列表中"""
        return ProcessFlowAssistant.add_tool_item(self, index, tool_name, tool_image_path)

    def update_progress(self):
        """更新进度显示"""
        return ProcessFlowAssistant.update_progress(self)

    def play_voice(self):
        """播放当前步骤语音"""
        return ProcessFlowAssistant.play_voice(self)

    def play_voice_for_step(self, step_index):
        """播放指定步骤的语音"""
        return ProcessFlowAssistant.play_voice_for_step(self, step_index)

    def open_document(self):
        """打开相关文档"""
        return ProcessFlowAssistant.open_document(self)

    def prev_step(self):
        """上一步"""
        return ProcessFlowAssistant.prev_step(self)

    def confirm_step(self):
        """确认当前步骤完成"""
        return ProcessFlowAssistant.confirm_step(self)

    def show_welcome_content(self):
        """显示欢迎内容"""
        return ProcessFlowAssistant.show_welcome_content(self)


def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 设置全局样式表 - 简洁现代风格
    app.setStyleSheet("""
        QMainWindow {
            background-color: #fafafa;
        }
        QWidget {
            font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
        }
        QGroupBox {
            font-weight: 500;
            font-size: 17px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin-top: 12px;
            padding-top: 16px;
            background-color: #ffffff;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 16px;
            padding: 0 8px 0 8px;
            color: #333333;
            background-color: #ffffff;
        }
    """)

    window = ProcessFlowAssistant()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
